import {
  Controller,
  Post,
  Patch,
  Delete,
  Body,
  Headers,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { FirebaseAuthGuard } from './guards/firebase-auth.guard';
import { AuthDto, VerifyEmailDto } from './dto/auth.dto';
import { AUTH_TYPE } from '@prisma/client';
import { ApiTags, ApiBearerAuth, ApiHeader, ApiOperation } from '@nestjs/swagger';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post()
  @UseGuards(FirebaseAuthGuard)
  @ApiOperation({ summary: 'Authenticate user Login/Sign-Up' })
  @ApiBearerAuth()
  @ApiHeader({ name: 'authType', enum: AUTH_TYPE })
  @ApiHeader({ name: 'notificationToken', required: false })
  async authenticate(
    @Req() req: any,
    @Headers('authType') authType: AUTH_TYPE,
    @Headers('notificationToken') fcmToken?: string,
    @Body() authDto?: AuthDto,
  ) {
    return this.authService.authenticate(
      req.user.uid,
      authType,
      fcmToken,
      authDto,
    );
  }

  @Post('send-verification-email')
  @ApiOperation({ summary: 'Send verification email' })
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  async sendVerificationEmail(@Req() req: any) {
    return this.authService.sendVerificationEmail(req.user.uid);
  }

  @Patch('verify-email')
  @ApiOperation({ summary: 'Verify email with OTP' })
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  async verifyEmail(@Req() req: any, @Body() verifyEmailDto: VerifyEmailDto) {
    return this.authService.verifyEmail(req.user.uid, verifyEmailDto);
  }

  @Patch('fcm-token')
  @ApiOperation({ summary: 'FCM Token Update' })
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiHeader({ name: 'notificationToken' })
  async updateFcmToken(
    @Req() req: any,
    @Headers('notificationToken') fcmToken: string,
  ) {
    return this.authService.updateFcmToken(req.user.uid, fcmToken);
  }

  @Delete('logout')
  @ApiOperation({ summary: 'Logout user' })
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiHeader({ name: 'notificationToken', required: false })
  async logout(@Req() req: any, @Query('sessionId') sessionId?: string) {
    return this.authService.logout(req.user.uid, sessionId);
  }
}
