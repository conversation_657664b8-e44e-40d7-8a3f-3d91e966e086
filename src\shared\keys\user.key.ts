import { REQUEST } from '@nestjs/core';

export const userMessage = {
  USER_NOT_FOUND: 'User not exist',
  USER_ALREADY_EXIST: 'User already exist',
  USER_IS_DELETED: 'User is deleted',
  USER_DELETED_SUCCESS: 'User is deleted successfully',
  USER_PURCHASE_FAIL:
    'Since there were multiple failed attempts to provide 2FA code. Please try again after 10 minutes.',
  USER_EMAIL_EXIST: 'User already exist with this email',
  USER_MOBILE_EXIST: 'User already exist with this phone number',
  VALIDATION_FAILED: 'User validation failed',
  USER_TYPE_DIFFERENCE: 'user type not matched',
  USER_DETAILS_NOT_FOUND: 'user details not found',
  USER_DETAILS_IS_DELETED: 'user details is deleted',
  COMPLETE_PROFILE: 'Please complete your profile.',
  CANNOT_UPDATE_YOU: 'You can not change your status',
  NOT_ALLOWED: 'You are not allowed to do this operation',
};

export const ownerMessage = {
  USER_ALREADY_EXIST: 'Owner already exist',
  USER_NOT_FOUND: 'Owner not exist',
  USER_ADDED_SUCCESSFULLY: 'Owner added successfully',
  NOT_OWNER:
    'You are not allowed to perform this operation as you are not an admin.',
};

export const userSessionMessage = {
  USER_SESSION_NOT_FOUND: 'User session not found',
  USER_SESSION_IS_DELETED: 'User session is deleted',
};

export const documentMessage = {
  DOCUMENT_NOT_FOUND: 'Document not found',
  DOCUMENT_ALREADY_EXIST: 'Document already exist',
  DOCUMENT_IS_DELETED: 'Document is deleted',
};

export const notificationMessage = {
  NOTIFICATION_NOT_FOUND: 'Notification not found',
  NOTIFICATION_ALREADY_EXIST: 'Notification already exist',
  NOTIFICATION_IS_DELETED: 'Notification is deleted',
};

export const authMessages = {
  ACCOUNT_DEACTIVATED: 'User is disabled.',
  INVALID_EMAIL: 'Invalid email address.',
  INVALID_CREDENTIALS_ERROR: 'Invalid email or password.',
  AUTH_HEADER_NOT_FOUND: 'Authorization header not found.',
  AUTH_HEADER_IS_NOT_BEARER: `Authorization header is not of type 'Bearer'.`,
  INVALID_AUTH_HEADER_BEARER: `Authorization header value has too many parts. It must follow the pattern: 'Bearer xx.yy.zz' where xx.yy.zz is a valid JWT token.`,
  SESSION_NOT_FOUND: 'Session not found.',
  SESSION_ALREADY_EXPIRED: 'Session is already expired.',
  SESSION_EXPIRED: 'Session is expired.',
  USER_NOT_FOUND: 'User not found.',
  TOKEN_EXPIRED: 'Token is expired please try again',
  CONSUMER_USER_PHONE_VERIFIED: `Consumer User's phone is already verified.`,
  LOGOUT_SUCCESS: 'Logout successful.',
  EMAIL_EXIST: 'Entered email already exist in system.',
  PHONE_EXIST: 'Entered phone number already exist in system.',
  PASSWORD_PATTERN_ERROR: `Password must contain at least 1 uppercase letter 1 lowercase letter 1 digit and 1 special character.`,
  SAME_AS_OLD_PASSWORD_ERROR:
    'New password should not be same as current password.',
  USERNAME_EXIST: 'Entered usermame already exist.',
  USERNAME_AVAILABLE: 'Usermame available.',
  PASSWORD_MATCH_ERROR: 'Password and confirm password does not match.',
  TOKEN_IS_EXPIRED: 'Token has been expired. Please try again.',
  INVALID_PASSWORD_ERROR: 'Current password is incorrect.',
  ENTER_VALID_OTP: 'Invalid or expired OTP. Please try again.',
  OTP_IS_EXPIRED: 'OTP has been expired. Please try again.',
  PHONENUMBER_PATTERN_ERROR:
    'Phone numbers must be between 10 and 16 characters.',
  CODE_PATTERN_ERROR: 'Code must be between 1 and 3 characters.',
  SOCIAL_ERROR: 'You have to enter socialId as well as socialType.',
  SOCIAL_MATCH_TYPE: 'Social type does not match.',
  SOCIAL_MATCH_ID: 'Social id does not match.',
  EMAIL_MATCH: 'Email does not match.',
  AUTH_ERROR: 'Authentication token invalid.',
  SOCIAL: 'Please login from your social account',
  EMAIL: 'Please login from your Email Id',
  IS_EXIST:
    'Your Google/Apple account is not connected. Please create an account and connect your Google/Apple account during onboarding.',
  EMAIL_SEND_FAILED: 'Failed to send verification email',
  OTP_REQUEST_TOO_FREQUENT: 'Please wait before requesting a new OTP',
  EMAIL_ALREADY_VERIFIED: 'Email is already verified',
};

export const NotificationServiceMessages = {
  title: {
    CARD_EXPIRE_ALERT_30: `Smart-Card {uniqueId} expires in 30 days`,
    CERTIFICATE_EXPIRE_ALERT_30: `Certificate {uniqueId} expires in 30 days`,
    CARD_EXPIRE_ALERT_15: `Smart-Card {uniqueId} expires in 15 days`,
    CERTIFICATE_EXPIRE_ALERT_15: `Certificate {uniqueId} expires in 15 days`,
    CARD_EXPIRE_ALERT_7: `Smart-Card {uniqueId} expires in 7 days`,
    CERTIFICATE_EXPIRE_ALERT_7: `Certificate {uniqueId} expires in 7 days`,
    CARD_EXPIRED: `Smart-Card {uniqueId} has expired`,
    CERTIFICATE_EXPIRED: `Certificate {uniqueId} has expired`,
  },
  body: {
    CARD_EXPIRE_ALERT_30: `Your card is set to expire in 30 days. Please ensure it is renewed in time to avoid service disruption.`,
    CERTIFICATE_EXPIRE_ALERT_30: `Your certificate is nearing its expiration date in 30 days. Please take the necessary steps to renew it promptly.`,
    CARD_EXPIRE_ALERT_15: `Your card is set to expire in 15 days. Please renew it soon to prevent service interruption.`,
    CERTIFICATE_EXPIRE_ALERT_15: `Your certificate is approaching its expiration in 15 days. Please take action to renew it at the earliest.`,
    CARD_EXPIRE_ALERT_7: `Your card is set to expire in 7 days. Renew it immediately to avoid service disruption.`,
    CERTIFICATE_EXPIRE_ALERT_7: `Your certificate will expire in 7 days. Please renew it without delay to maintain uninterrupted services.`,
    CARD_EXPIRED: `Your card has expired. Please renew it immediately to continue using our services without interruption.`,
    CERTIFICATE_EXPIRED: `Your certificate has expired. Please renew it as soon as possible to avoid any inconvenience.`,
  },
  type: {
    CARD_EXPIRE_ALERT_30: `card_expire_alert_30`,
    CERTIFICATE_EXPIRE_ALERT_30: `certificate_expire_alert_30`,
    CARD_EXPIRE_ALERT_15: `card_expire_alert_15`,
    CERTIFICATE_EXPIRE_ALERT_15: `certificate_expire_alert_15`,
    CARD_EXPIRE_ALERT_7: `card_expire_alert_7`,
    CERTIFICATE_EXPIRE_ALERT_7: `certificate_expire_alert_7`,
    CARD_EXPIRED: `card_expired`,
    CERTIFICATE_EXPIRED: `certificate_expired`,
  },
};
