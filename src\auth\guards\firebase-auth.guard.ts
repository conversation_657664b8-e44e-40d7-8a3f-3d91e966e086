import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { authMessages } from '../../shared/keys/user.key';

@Injectable()
export class FirebaseAuthGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException(authMessages.AUTH_HEADER_NOT_FOUND);
    }

    if (!authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException(authMessages.AUTH_HEADER_IS_NOT_BEARER);
    }

    const token = authHeader.split(' ')[1];
    
    try {
      const decodedToken = await admin.auth().verifyIdToken(token);
      request.user = decodedToken;
      return true;
    } catch (error) {
      throw new UnauthorizedException(authMessages.AUTH_ERROR);
    }
  }
}
