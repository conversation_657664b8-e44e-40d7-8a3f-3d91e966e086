import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { PrismaService } from '../shared/module/prisma/prisma.service';
import { EmailService } from '../shared/services/email.service';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [
    ConfigModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, PrismaService, EmailService],
  exports: [AuthService],
})
export class AuthModule {}
